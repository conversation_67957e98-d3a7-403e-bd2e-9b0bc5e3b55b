package qms.framework.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import qms.framework.dto.ConditionalDictionary;
import qms.framework.dto.ConditionalDictionaryEntry;
import qms.framework.dto.ConditionalFieldDTO;
import qms.framework.dto.ConditionalFieldData;
import qms.framework.enums.ConditionalFieldType;

public class ConditionalUtil {    
    
    private static ConditionalFieldData parseField(final Integer currentOrder, final List<ConditionalFieldDTO> currentOrderFields) {
        final List<ConditionalFieldDTO> matchFields = currentOrderFields.stream()
                .filter((field) -> Objects.equals(field.getType(), ConditionalFieldType.MATCH_FIELD.getValue()))
                .collect(Collectors.toList());
        final List<ConditionalFieldDTO> assignedFields = currentOrderFields.stream()
                .filter((field) -> Objects.equals(field.getType(), ConditionalFieldType.ASSIGNED_FIELD.getValue()))
                .collect(Collectors.toList());
        final ConditionalFieldData value = new ConditionalFieldData(matchFields, assignedFields, currentOrder);
        return value;
    }

    private static List<ConditionalFieldData> parseFields(final List<ConditionalFieldDTO> conditionalFields) {
        final List<ConditionalFieldData> values = new ArrayList<>();
        if (conditionalFields == null || conditionalFields.isEmpty()) {
            return values;
        }
        final Integer lengthConditions = conditionalFields.stream()
                .map((field) -> field.getFieldOrder())
                .reduce(Math::max)
                .orElse(0) + 1;
        for (Integer currentOrder = 0; currentOrder < lengthConditions; currentOrder++) {
            final Integer fieldOrder = currentOrder;
            final List<ConditionalFieldDTO> currentOrderFields = conditionalFields.stream()
                    .filter((field) -> Objects.equals(field.getFieldOrder(), fieldOrder))
                    .collect(Collectors.toList());
            final ConditionalFieldData currentValue = parseField(currentOrder, currentOrderFields);
            values.add(currentValue);
        }
        values.sort((a, b) -> {
            if (+a.getFieldOrder() < +b.getFieldOrder()) {
                return -1;
            }
            if (+a.getFieldOrder() > +b.getFieldOrder()) {
                return 1;
            }
            return 0;
        });
        return values;
    }

    public static ConditionalDictionary parseDictionary(final List<ConditionalFieldDTO> conditionalFields) {
      final List<ConditionalFieldData> sources = parseFields(conditionalFields);
      final List<ConditionalDictionaryEntry> entries = sources.stream()
            .map((entry) -> new ConditionalDictionaryEntry( 
                entry.getFieldOrder(),
                entry.getMatchFields().stream().map((field) -> field.getFieldName()).collect(Collectors.toSet()),
                entry.getAssignedFields().stream().map((field) -> field.getFieldName()).collect(Collectors.toSet())
            )).collect(Collectors.toList());
      entries.stream().flatMap((entity) -> entity.getMatchFields().stream()).collect(Collectors.toSet());
      final Set<String> matchFields = entries.stream().flatMap((entity) -> entity.getMatchFields().stream()).collect(Collectors.toSet());
      final Set<String> assignedFields = entries.stream().flatMap((entity) -> entity.getAssignedFields().stream()).collect(Collectors.toSet());
      final ConditionalDictionary dictionary = new ConditionalDictionary(sources, entries, matchFields, assignedFields);
      return dictionary;
    }

}
