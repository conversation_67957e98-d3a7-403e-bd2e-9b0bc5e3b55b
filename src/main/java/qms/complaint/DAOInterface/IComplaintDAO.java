package qms.complaint.DAOInterface;

import DPMS.Mapping.Complaint;
import Framework.Config.ITextHasValue;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.util.List;
import qms.access.dto.LoggedUser;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
@Implementation(name = "ComplaintDAO")
public interface IComplaintDAO  extends IGenericDAO<Complaint, Long> {

    public List getComplaintsManager(Long complaintId);

    public String getCSComplaintsManager(Long complaintId);

    public Complaint notApplyComplaint(Complaint complaint, LoggedUser loggedUser);

    public String getComboSourceComplaint(String selected);

    public String getComboDepartmentComplaint(String selected, Long loggedUserId, boolean isAdmin);

    public String getSourceComplaint(Long complaintId);

    public String getClasification(String intTipoId);

    public String getClasificationSelected(Long tipoId);

    public String comboPriority(String selection, Long valueDefaultId);

    public Complaint reportedComplaint(Complaint complaint, String filesIds, LoggedUser loggedUser) throws QMSException;

    public Complaint assignedComplaint(Complaint complaint, LoggedUser loggedUser);

    public Complaint attendedComplaint(Complaint complaint, LoggedUser loggedUser);

    public Complaint verifiedComplaint(Complaint complaint, LoggedUser loggedUser);

    public Complaint evaluatedComplaintSatisfacted(Complaint complaint, LoggedUser loggedUser);

    public Complaint evaluatedUnsatisfactoryComplaint(Complaint complaint, LoggedUser loggedUser) throws QMSException;
    
    public List<ITextHasValue> getComplaintsManagerInBusinessUnit(Long businessUnit);
    
    public String getPriorityJSON();
    
    public String getClauseTypeJSON();
    
    public String getFindingTypeJSON();

    public Complaint changeDepartment(Long complaintId, Long departmentId, LoggedUser loggedUser);
    
    public Complaint rejectComplaintAnswer(Complaint complaintEnt, LoggedUser loggedUser);
        
    public void saveFiles(final Long complaintId, final String filesIds, final Long loggedUserId);
    
    public Complaint returnedComplaint(Complaint complaint, String newStatus, LoggedUser loggedUser);
}
