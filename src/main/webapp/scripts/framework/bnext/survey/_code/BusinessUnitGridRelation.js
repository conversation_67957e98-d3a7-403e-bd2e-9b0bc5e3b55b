define([
    'dojo/dom', 'bnext/gridComponent',
    'bnext/i18n!bnext/administrator/surveys/nls/surveycapture.events',
    'bnext/survey/_util/jsFailureExposure!',
    'dojo/domReady!'], 
    function(dom, GridComponent, i18n) {

    var
            org_unit = ({
                title: i18n.colCorp,
                type: columnTypes.text,
                attributes: {style: {width: "150px"}},
                id: "organizationalUnit.description",
                searchObj: {
                    type: 'texto',
                    col: 1
                }
            }),
            push = function(id) {
                grid_business_unit_to_add.popRowById(id);
                grid_business_unit_to_add.updateDataInfo();
            },
            pop = function(id) {
                grid_business_unit_added.popRowById(id);
                grid_business_unit_to_add.updateDataInfo();
            },
            grid_business_unit_to_add = new GridComponent({
                id: "grid_business_unit_to_add",
                container: "datagrid_business_unit_to_add",
                resultsInfo: "#auto",
                paginationInfo: "#auto",
                serviceStore: "../DPMS/SurveyDataSource.action",
                methodName: "getRowsSearchUNEs",
                harvestCriteria: false,
                noExcel: true,
                noEyes: true,
                noPagination: true,
                columns: {
                    is: "#auto",
                    editRecordActionDefault: push,
                    editRecordLabel: i18n.add,
                    editRecordIcon: "add.png",
                    extraColumns: [org_unit]
                }
            }),
            grid_business_unit_added = new GridComponent({
                id: "grid_business_unit_added",
                container: "datagrid_business_unit_added",
                resultsInfo: "#auto",
                paginationInfo: "#auto",
                noRegMessage: i18n.noResultFound,
                serviceStore: "../DPMS/SurveyDataSource.action?currentEntityId=" + dom.byId('globalId').value,
                methodName: "getRowsAgregadosUne",
                harvestCriteria: false,
                noExcel: true,
                noEyes: true,
                noPagination: true,
                columns: {
                    is: "#auto",
                    editRecordActionDefault: pop,
                    editRecordLabel: i18n.delete,
                    editRecordIcon: "delete.png",
                    extraColumns: [org_unit]
                }
            });
    grid_business_unit_added.setPageSize(15);
    grid_business_unit_added.linkGrid(grid_business_unit_to_add);



    return {
        added: grid_business_unit_added,
        to_add: grid_business_unit_to_add
    };
});