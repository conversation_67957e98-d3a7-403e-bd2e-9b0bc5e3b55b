<%-- 
    Document   : escalation-list
    Created on : 4/10/2016, 03:03:07 PM
    Author     : <PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
    <jsp:include page="../../components/requiredScripts.jsp" />
    <script src="../scripts/framework/bnext/administrator/escalation/escalation-list.js?${systemVersion}" type="text/javascript"></script>
    <style>
        .selector {
            display: inline-block;
            text-align: center;
            padding: 4px 8px;
            margin: 2px 2px 0px 2px;
            background: #F0F0F0;
            -moz-border-radius: 3px;
            -webkit-border-radius: 3px;
            border-radius: 3px;
            cursor: pointer;
            color: #000;
            border: 1px solid #000;
        }
        .selector:hover, .css_exceptionScalable:hover {
            background: #CCC;
        }
        .selector img{float: left;height: 16px;}
        .selector span{line-height:16px;}
        .css_changeBoss, .css_saveSingle, .css_clearSingle, .css_exceptionScalable {
            text-align: center;
        }
        .css_changeBoss img, .css_saveSingle img, .css_clearSingle img, .css_exceptionScalable {
            cursor: pointer;
        }
        .exception-to-save {
            background-color: lightpink!important;
        }
        .exception-to-save td.css_saveSingle span img,
        .saved-yes-exception td.css_clearSingle span img {
            display: inline!important;
        }
        .scalability-off-to-save .css_changeBoss span,
        .scalable-label-off .on, .scalable-label-on .off {
            display: none;
        }
        #floatingOptionsButtons {
            display: none;
        }

        @media screen and (max-width: 39.9375em) {
            #floatingOptionsButtons {
                display: block;
            }
            .hideOnMobile {
                display: none;
            }
        }
    </style>
</head>
<body writingsuggestions="false" textprediction="false">
    <%@include file="../../components/loader.jsp" %>
    <div class="grid-container grid-floating-active full-width-grid-component">
        <div class="grid-x">
            <div class="cell">
                <form class="container-form grid-floating-action-buttons">
                    <div class="header grid-container">
                        <div class="grid-x content_title">
                            <div class="cell position-relative">
                                <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                </h3>
                                <div class="float-right">
                                    <!-- boton de opciones -->
                                </div> 
                            </div>
                            <h5 class="cell igx-card-header__subtitle">
                            </h5>
                        </div>
                    </div>
                    <div class="floating-action-buttons hideOnPrint hideOnMobile" id="selectorSeparator">
                        <div class="fab-button fixed-button">
                            <span class="material-icons">save</span>
                            <span id="saveAllBtn"></span>
                        </div>
                        <div class="fab-button fixed-button">
                            <span class="material-icons">clear</span>
                            <span id="clearAllBtn"></span>
                        </div>
                        <div class="selector fab-button displayNone Button fixed-button">
                            <img src="../scripts/framework/bnext/images/upload.png">
                            <span id="manualBtn"></span>
                        </div>
                    </div>
                    <div class ="grid-container table_workarea">
                        <ul class="grid-x grid-padding-x content_area">
                            <li><div id="search"></div></li>
                            <li><div id="tabNode" ></div></li>
                            <li><table id="dataGrid"></table></li>
                            <li><div id="pagination"></div></li>
                        </ul>
                    </div>
                    <div id="floatingOptionsButtons"></div>
                    <div class ="displayNone">
                        <%@include file="../../components/sessionVariables.jsp" %>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div dojoType="dijit.Dialog" id="searchBoss" data-dojo-props="draggable: false">
        <ul class="content_area">
            <li tabindex="0">
                <table id="bossSearch"></table>
            </li>
            <li class="actionButtons">
                <input class="button raised-button" type="button" id="bossSearchEndBtn" value=""/>
                <input class="button" type="button" id="bossSearchCancelBtn" value=""/>
                <input type="hidden" id="selectedBossId" value=""/>
            </li>
        </ul>
        <s:hidden name="systemColor" id="SYSTEM_COLOR" />
    </div>
    <%@include file="../../components/footer.jsp" %>
</body>
</html>