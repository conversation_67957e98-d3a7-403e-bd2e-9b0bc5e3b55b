package qms.custom.dto;

import java.util.Objects;
import jakarta.persistence.Transient;
import qms.custom.core.IDynamicTableField;

/**
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class DynamicFieldDTO extends BaseCustomField implements IDynamicTableField<String>, Comparable<DynamicFieldDTO> {

    private static final long serialVersionUID = 1L;

    private String label;
    private String columnType;
    private Integer status;
    private Integer mandatory;
    private Integer searchAlwaysVisible;
    private String[] options;
    private Long order;

    public DynamicFieldDTO(String name, String type) {
        super(name, type);
    }

    public DynamicFieldDTO(String name, String type, Boolean allowNulls) {
        super(name, type, allowNulls);
    }

    public DynamicFieldDTO(String name, String type, Boolean allowNulls, Boolean isSystem) {
        super(name, type, allowNulls, isSystem);
    }

    public DynamicFieldDTO(Long id, String name, String label, Integer status, Integer mandatory, Integer searchAlwaysVisible, String type, String columnType) {
        super(id, name, type, true);
        this.label = label;
        this.status = status;
        this.mandatory = mandatory;
        this.searchAlwaysVisible = searchAlwaysVisible;
        this.columnType = columnType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMandatory() {
        return mandatory;
    }

    public void setMandatory(Integer mandatory) {
        this.mandatory = mandatory;
    }

    public Integer getSearchAlwaysVisible() {
        return searchAlwaysVisible;
    }

    public void setSearchAlwaysVisible(Integer searchAlwaysVisible) {
        this.searchAlwaysVisible = searchAlwaysVisible;
    }

    public String[] getOptions() {
        return options;
    }

    public void setOptions(String[] options) {
        this.options = options;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }

    @Override
    public String toString() {
        return "DynamicFieldDTO{" + "id=" + getId() + ", name=" + getName() + ", label=" + label + ", type=" + getType() + '}';
    }

    @Override
    public int compareTo(DynamicFieldDTO o) {
        if (order == null) {
            return -1;
        }
        if (o == null || o.getOrder() == null) {
            return 1;
        }
        if (order == null) {
            return 0;
        }
        return order.compareTo(o.order);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DynamicFieldDTO other = (DynamicFieldDTO) obj;
        return Objects.equals(this.getName(), other.getName());
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 79 * hash + Objects.hashCode(this.getName());
        return hash;
    }

    public String getColumnType() {
        return columnType;
    }

    public void setColumnType(String columnType) {
        this.columnType = columnType;
    }


    @Transient
    @Override
    public Integer getMaxLength() {
        if (this.columnType == null) {
            return super.getMaxLength();
        }
        switch (this.columnType) {
            case "varchar(4000)":
                if (Objects.equals(this.getType(), "markdown")) {
                    return 3500; // <-- El `markdown` único campo con la discrepacia
                }
                return 4000;
            case "varchar(255)":
                return 255;
            case "varchar(8000)":
                if (Objects.equals(this.getType(), "markdown")) {
                    return 3500; // <-- El `markdown` único campo con la discrepacia
                }
                return 8000;
            default:
                return super.getMaxLength();
        }
    }   
}
