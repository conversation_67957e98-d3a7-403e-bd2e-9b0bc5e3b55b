package qms.form.dao;

import DPMS.Mapping.Document;
import Framework.Config.TextLongValue;
import Framework.DAO.GenericDAOImpl;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.form.dto.FormPublishMigrationColumnDTO;
import qms.form.dto.FormPublishMigrationColumnDataSourceDTO;
import qms.form.entity.FormPublishMigrationColumn;

/**
 * <AUTHOR>
 */
@Lazy
@Repository(value = "FormPublishMigrationDAO")
@Scope(value = "singleton")
public class FormPublishMigrationDAO extends GenericDAOImpl<FormPublishMigrationColumn, Long> implements IFormPublishMigrationDAO {

    /**
     * Get the data source for the migration window to work.
     * - Get the available documents.
     * - Get the available migration fields.
     *
     * @param documentMasterId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public FormPublishMigrationColumnDataSourceDTO getDataSource(String documentMasterId) {
        FormPublishMigrationColumnDataSourceDTO dataSource = new FormPublishMigrationColumnDataSourceDTO();
        dataSource.setRootDocuments(getRootDocuments(documentMasterId));
        dataSource.setMigrationFields(getMigrationFields(documentMasterId));
        return dataSource;
    }

    private Set<FormPublishMigrationColumnDTO> getMigrationFields(String documentMasterId) {
        final List<FormPublishMigrationColumnDTO> result = HQLT_findByQuery(FormPublishMigrationColumnDTO.class, " " +
            " SELECT new " + FormPublishMigrationColumnDTO.class.getCanonicalName() + "(" +
                " id" +
                ", status" +
                ", code" +
                ", description" +
                ", rootDocumentId" +
                ", targetDocumentId" +
                ", rootDocumentMasterId" +
                ", rootDocumentVersion" +
                ", rootFieldColumnName" +
                ", targetDocumentVersion" +
                ", targetFieldColumnName" +
            " ) " +
            " FROM " + FormPublishMigrationColumn.class.getCanonicalName() + " d " +
            " WHERE " +
                " d.deleted = 0" +
                " AND d.rootDocumentMasterId = :documentMasterId",
    "documentMasterId", documentMasterId

        );
        return new LinkedHashSet<>(result);
    }

    private Set<TextLongValue> getRootDocuments(String documentMasterId) {
        final List<TextLongValue> result = HQLT_findByQuery(TextLongValue.class, " " +
            " SELECT new " + TextLongValue.class.getCanonicalName() + "(" +
                " d.version" +
                ",d.id" +
            " ) " +
            " FROM " + Document.class.getCanonicalName() + " d " +
            " WHERE " +
            " d.deleted = 0" +
            " AND d.masterId = :documentMasterId",
    "documentMasterId", documentMasterId

        );
        return new LinkedHashSet<>(result);
    }
}
