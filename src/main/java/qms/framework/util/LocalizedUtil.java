package qms.framework.util;

import DPMS.Mapping.Persistable;
import DPMS.Mapping.Settings;
import DPMS.Mapping.SupportedLanguages;
import Framework.Action.SessionViewer;
import Framework.Config.BaseDomainObject;
import Framework.Config.CompositeStandardEntity;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.licensing.LicenseUtil;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.metamodel.Attribute;
import jakarta.persistence.metamodel.EntityType;
import jakarta.servlet.ServletContext;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.dto.ElapsedDataDTO;
import qms.util.EntityCommon;
import qms.util.QMSException;
import qms.util.ReflectionUtil;
import static Framework.DAO.GenericDAOImpl.IS_SQL_SERVER;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class LocalizedUtil {

    private static final Class<LocalizedUtil> CLAZZ = LocalizedUtil.class;
    private static Map<String, LocalizedEntityDTO> LOCALIZED_TABLES = null;
    private static Set<String> SUPPORTED_LANGUAGES = null;
    private static Set<ITextHasValue<String>> SUPPORTED_LANGUAGES_SET = null;
    private static Map<String, String> ENTITY_TABLE_NAMES;    
    private static ConcurrentHashMap<String, String> LOCALIZED_SQL_QUERY = null;
    private static ConcurrentHashMap<String, Set<String>> LOCALIZED_SQL_INSERT = null;
    private static ConcurrentHashMap<String, String> LOCALIZED_SQL_UPDATE = null;
    private static Map<String, Set<LocalizedFieldDTO>> LOCALIZED_FIELDS = null;
    private static Map<String, Set<LocalizedFieldDTO>> NOT_LOCALIZED_FIELDS = null;
    private static final String FIND_TABLES_MATCH = "(?<=from\\s|join\\s)(\\w+)(\\s*)(\\w*)";
    private static final String EXCLUDE_MATCH = " i18n ";
    private static Pattern FIND_TABLES_PATTERN = null;
    private static Pattern EXCLUDE_PATTERN = null;
    private static final AtomicBoolean INITIALIZED_STARTED = new AtomicBoolean(false);
    private static final AtomicBoolean INITIALIZED_COMPLETED = new AtomicBoolean(false);
    private static final Logger LOGGER = Loggable.getLogger(CLAZZ);
    private static LocalizedTranslate I18N = null;
    
    private static void clear(){
        if (LOCALIZED_TABLES != null) {
            LOCALIZED_TABLES.clear();
        }
        if (SUPPORTED_LANGUAGES != null) {
            SUPPORTED_LANGUAGES.clear();
        }
        if (SUPPORTED_LANGUAGES_SET != null) {
            SUPPORTED_LANGUAGES_SET.clear();
        }
        if (LOCALIZED_SQL_QUERY != null) {
            LOCALIZED_SQL_QUERY.clear();
        }
        if (ENTITY_TABLE_NAMES != null) {
            ENTITY_TABLE_NAMES.clear();
        }
        if (LOCALIZED_SQL_INSERT != null) {
            LOCALIZED_SQL_INSERT.clear();
        }
        if (LOCALIZED_SQL_UPDATE != null) {
            LOCALIZED_SQL_UPDATE.clear();
        }
        if (LOCALIZED_FIELDS != null) {
            LOCALIZED_FIELDS.clear();
        }
        if (NOT_LOCALIZED_FIELDS != null) {
            NOT_LOCALIZED_FIELDS.clear();
        }
    }
    
    public static void intitialize(ServletContext servletContext) throws QMSException, NoSuchMethodException, InterruptedException {
        if (!Utilities.areEntitiesLocalized()) {
            clear();
            return;
        }
        if (INITIALIZED_STARTED.compareAndSet(false, true)) {
            ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
            IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
            setupMetadata(servletContext);
            loadSupportedLanguages(dao);
            I18N = new LocalizedTranslate(LOCALIZED_TABLES, SUPPORTED_LANGUAGES, LOCALIZED_FIELDS, NOT_LOCALIZED_FIELDS);
            initializeDataStructures(dao);
            MeasureTime.stop(tStart, "Elapsed time initilizing Localize Component");
            INITIALIZED_COMPLETED.set(true);
        }
    }
    
    public static void reset(Integer localizeEntities, ServletContext servletContext) throws QMSException, NoSuchMethodException, InterruptedException {
        final Settings settings = Utilities.getSettings(servletContext);
        if (!Objects.equals(localizeEntities, settings.getLocalizeEntities())) {
            INITIALIZED_STARTED.set(false);
            if (settings.getLocalizeEntities() == 1) {
                EntityCommon.resetEntityTypes(servletContext);
                intitialize(servletContext);
            } else {
                clear();
            }
        }
    }
    
    public static String processQuery(String sql) {
        if (!Utilities.areEntitiesLocalized()) {
            return sql;
        }
        if (validStartup()) {
            return sql;
        }
        Matcher matcher = EXCLUDE_PATTERN.matcher(sql);
        if (matcher.find()) {   
            return sql;
        }
        matcher = FIND_TABLES_PATTERN.matcher(sql);   
        String locale = null;
        StringBuffer sb = new StringBuffer(sql.length());
        while (matcher.find()) {
            if (locale == null) {
                locale = loadLocale();
                if (locale == null) {
                    return sql;
                }
            }
            final String tableName = matcher.group(1); 
            final String alias = matcher.group(3);
            if (LOCALIZED_TABLES.containsKey(tableName)) {
                matcher.appendReplacement(sb, generateLocalizedQuery(tableName, locale));
                if (alias == null || alias.trim().isEmpty()) {
                    sb.append(" i18n ");
                } else {
                    if ("FROM".equals(alias.toUpperCase()) || "ON".equals(alias.toUpperCase())) {
                        sb.append(" i18n ");
                    }
                    sb.append(" ").append(alias).append(" ");
                }
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    public static void insertLanguages(BaseDomainObject<?> entity) {
        if (!Utilities.areEntitiesLocalized()) {
            return;
        }
        final String className = entity.getClass().getCanonicalName();
        final String tableName = ENTITY_TABLE_NAMES.get(className);
        if (tableName == null || !LOCALIZED_TABLES.containsKey(tableName)) {
            return;
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        final Set<String> sqls;
        if (LOCALIZED_SQL_INSERT.containsKey(tableName)) { 
            sqls = LOCALIZED_SQL_INSERT.get(tableName);
        } else {
            sqls = new LinkedHashSet<>(10);
            I18N.generateInserts(sqls, tableName);
            LOCALIZED_SQL_INSERT.putIfAbsent(tableName, sqls);
        }
        Set<String> executeSqls = new LinkedHashSet<>(sqls.size());  
        LocalizedEntityDTO table = LOCALIZED_TABLES.get(tableName);
        sqls.forEach(sql -> {
            if (table.isCompositeId()) {
                String executeSql = sql;
                final Set<LocalizedFieldDTO> idNames = table.getCompositeIdNames().stream()
                        .filter(idName -> className.equals(idName.getEntityName()))
                        .collect(Collectors.toSet());
                for (LocalizedFieldDTO identityPk : idNames) {
                    String id = ReflectionUtil.getValue(entity.identifuerValue(), identityPk.getHqlField()).toString();
                    executeSql = executeSql.replaceAll(":" + identityPk.getSqlField(), id);
                }
                executeSqls.add(executeSql);
            } else {
                executeSqls.add(sql.replaceAll(":recordId", ((Persistable) entity).getId().toString()));
            }
        });
        
        final String sql = StringUtils.join(executeSqls, " ");
        if (sql != null && !sql.isEmpty()) {
            try {
                LOGGER.debug("UPDATING i18n DATA OF ENTITY: {} WITH FOLLOWING QUERY\r\n /{}", entity.toString(), sql);
                dao.SQL_execute(sql);
                entity.setLocalized(true);
            } catch (QMSException ex) {
                LOGGER.error("FAIL UPDATING i18n DATA OF ENTITY: {} WITH FOLLOWING QUERY\\r\\n /{}", entity.toString(), sql, ex);
            }
        }  
        MeasureTime.stop(tStart, "Elapsed time inserting languages values for:" +  entity.toString());
    }
    
    public static void updateLanguages(BaseDomainObject<?> entity) {
        if (!Utilities.areEntitiesLocalized() || entity.isLocalized()) {
            return;
        }
        final String className = entity.getClass().getCanonicalName();
        final String tableName = ENTITY_TABLE_NAMES.get(className);
        if (tableName == null || !LOCALIZED_TABLES.containsKey(tableName)) {
            return;
        }
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        final String locale = loadLocale();
        if(locale == null) {
            return;
        }
        IUntypedDAO dao = Utilities.getUntypedDAO();
        Map<String, Object> params = new HashMap<>(10);
        I18N.setProperties(LOCALIZED_FIELDS.get(tableName), entity, params);  
        LocalizedEntityDTO table = LOCALIZED_TABLES.get(tableName);
        if (table.isCompositeId()) {   
            table.getCompositeIdNames().stream()
                    .filter(idName -> className.equals(idName.getEntityName()))
                    .forEach(identityPk -> {
                        String id = ReflectionUtil.getValue(entity.identifuerValue(), identityPk.getHqlField());
                params.put(identityPk.getSqlField(), id);
                    });
        } else {
            params.put("recordId", ((Persistable) entity).getId());
        }
        entity.setLocalized(true);
        executeUpdateLanguage(tableName, locale, params, dao);
        MeasureTime.stop(tStart, "Elapsed time updating language values for :" +  entity.toString());
    }
    
    public static Set<LocalizedFieldDTO> loadLocalizedFields(String auditableEntityImpl) {
        if (!Utilities.areEntitiesLocalized()) {
            return new LinkedHashSet<>(0);
        }
        final String tableName = ENTITY_TABLE_NAMES.get(auditableEntityImpl);
        if (tableName == null) {
            LOGGER.error("Not found localized table for entity {}", auditableEntityImpl);
            return new LinkedHashSet<>(0);
        }   
        final Set<LocalizedFieldDTO> fields = LOCALIZED_FIELDS.get(tableName);
        if (fields == null) {
            LOGGER.error("Not found localized fields for entity {}", auditableEntityImpl);
            return new LinkedHashSet<>(0);
    }
        return fields.stream()
                .filter(field -> auditableEntityImpl.equals(field.getEntityName()))
                .collect(Collectors.toSet());
    }

    public static Set<ITextHasValue<String>> getSupportedLanguages() {
        if (!Utilities.areEntitiesLocalized()) {
            return new HashSet<>(0);
        }
        return Collections.unmodifiableSet(SUPPORTED_LANGUAGES_SET);
    }
    
    public static ITextHasValue<String> getLocaleByKey(String locale) {
        if (!Utilities.areEntitiesLocalized()) {
            return null;
        }
        if (SUPPORTED_LANGUAGES_SET.size() <= 1) {
            LocalizedUtil.loadSupportedLanguages(
                    Utilities.getUntypedDAO()
            );
        }
        for (ITextHasValue<String> lang : SUPPORTED_LANGUAGES_SET) {
            if (locale.equals(lang.getValue())) {
                return lang;
            }
        }
        return null;
    }

    public static Map<String, String> loadLocalizedEntity(String className, Long id, IUntypedDAO dao) {
        if (!Utilities.areEntitiesLocalized()) {
            return new HashMap<>(0);
        }
        final String tableName = ENTITY_TABLE_NAMES.get(className);
        if (tableName == null) {
            return new HashMap<>(0);
        }
            StringBuilder sb = new StringBuilder(50);
        LOCALIZED_FIELDS.get(tableName).stream()
                .filter(field -> className.equals(field.getEntityName()))
                .forEach(field -> {
                sb.append(", c.").append(field.getHqlField()).append(" as ").append(field.getHqlField()).append(" ");
                });
            return (Map<String, String>)dao.HQL_findSimpleObject(""
                    + " SELECT new Map(c.id as id " + sb.toString() + ")"
                    + " FROM " + className + " c"
                    + " WHERE c.id = :id", "id", id);
        }

    public static Map<String, String> loadLocalizedEntity(String className, Map<String, Long> ids, IUntypedDAO dao) {
        if (!Utilities.areEntitiesLocalized()) {
            return new HashMap<>(0);
        }
        final String tableName = ENTITY_TABLE_NAMES.get(className);
        if (className == null) {
            return new HashMap<>(0);
        }                
            StringBuilder sb = new StringBuilder(50);
        LOCALIZED_FIELDS.get(tableName).stream()
                .filter(field -> className.equals(field.getEntityName()))
                .forEach(field -> {
                sb.append(", c.").append(field.getHqlField()).append(" as ").append(field.getHqlField()).append(" ");
                });
            boolean first = true;
            StringBuilder sqlBuilder = new StringBuilder(50);
            for(Map.Entry<String, Long> entrySet : ids.entrySet()) {
                if (first) {
                    first = false;
                } else {
                    sqlBuilder.append(" AND ");
                }
                sqlBuilder.append(" c.id.").append(entrySet.getKey()).append(" = :").append(entrySet.getKey());
            }
            return (Map<String, String>)dao.HQL_findSimpleObject(""
                    + " SELECT new Map(c.id as id " + sb.toString() + ")"
                    + " FROM " + className + " c"
                    + " WHERE " + sqlBuilder.toString(), ids);
        }
    
    public static GenericSaveHandle saveLocalized(Map<String, Object> params, Map<String, List<Map<String, String>>> data, String className, IUntypedDAO dao) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (!Utilities.areEntitiesLocalized()) {
            gsh.setErrorMessage("LOCALIZE_ENTITIES_DISABLED");
            return gsh;
        }
        try {
            final String tableName = ENTITY_TABLE_NAMES.get(className);
            if (tableName == null) {
                return gsh;
            }
            LocalizedEntityDTO table = LOCALIZED_TABLES.get(tableName);
            for(Map.Entry<String, List<Map<String, String>>> entrySet : data.entrySet()) {
                String locale = entrySet.getKey();
                List<Map<String, String>> fields = entrySet.getValue();
                if (!LOCALIZED_TABLES.containsKey(tableName)) {
                    throw new QMSException("Trying to save not localized table " + tableName);
                }                
                final Map<String, Object> compositeParams;
                if (table.isCompositeId()) {
                    final Set<LocalizedFieldDTO> idNames = table.getCompositeIdNames().stream()
                            .filter(idName -> className.equals(idName.getEntityName()))
                            .collect(Collectors.toSet());
                    compositeParams = I18N.setIdProperties(params, idNames);
                } else {
                    compositeParams = params;
                }
                I18N.setProperties(fields, compositeParams);
                executeUpdateLanguage(tableName, locale, compositeParams, dao);
            }
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(SessionViewer.EDIT_SUCCESS);
            return gsh;
        } catch (QMSException ex) {
            LOGGER.error("Trying to save localized items fro class {}", className, ex);
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage(ex.getMessage());
            return gsh;
        }
    }
    
    private static boolean validStartup() {
        if (!INITIALIZED_COMPLETED.get()) {
            LOGGER.warn("LocalizedUtil.intitialize not called yet");
            return true;
        }
        return false;
    }
    
    private static void executeUpdateLanguage(String tableName, final String locale, Map<String, Object> params, IUntypedDAO dao) {
        String sql;
        final String tableKey = tableName + "_" + locale;
        if (LOCALIZED_SQL_UPDATE.containsKey(tableKey)) { 
            sql = LOCALIZED_SQL_UPDATE.get(tableKey);
        } else { 
            sql = I18N.generateUpdate(tableName, locale);
            LOCALIZED_SQL_UPDATE.putIfAbsent(tableKey, sql);
        }
        if (sql != null && !sql.isEmpty()) {
            LOGGER.debug("UPDATING i18n DATA OF ENTITY: {} FOR LOCALE : {} WITH FOLLOWING QUERY\r\n /{}", tableName, locale, sql);
            dao.SQL_updateByQuery(
                sql,
                params,
                0,
                Collections.singletonList(tableKey)
            );
        }  
    }
    
    private static String generateLocalizedQuery(String tableName, String lang) {
        final String localeKey = tableName + "_" + lang;
        if (LOCALIZED_SQL_QUERY.containsKey(localeKey)) {
            return LOCALIZED_SQL_QUERY.get(localeKey);
        }
        String sql = I18N.selectQuery(tableName, lang);
        LOCALIZED_SQL_QUERY.put(localeKey, sql);
        return sql;
    }
    
    private static void setupMetadata(ServletContext servletContext) throws NoSuchMethodException {
        clear();
        LOCALIZED_TABLES = new HashMap<>(10);
        LOCALIZED_SQL_QUERY = new ConcurrentHashMap<>(10);
        LOCALIZED_SQL_INSERT = new ConcurrentHashMap<>(10);
        LOCALIZED_SQL_UPDATE = new ConcurrentHashMap<>(10);
        LOCALIZED_FIELDS = new HashMap<>(10);
        NOT_LOCALIZED_FIELDS = new HashMap<>(10);
        FIND_TABLES_PATTERN = Pattern.compile(FIND_TABLES_MATCH, Pattern.CASE_INSENSITIVE);
        EXCLUDE_PATTERN = Pattern.compile(EXCLUDE_MATCH, Pattern.CASE_INSENSITIVE);
        ENTITY_TABLE_NAMES = new HashMap<>(10);
        loadLocalizedEntities(servletContext);
    }
    
    private static void initializeDataStructures(IUntypedDAO dao) throws QMSException, InterruptedException {
        if(!IS_SQL_SERVER) {
            throw new QMSException("La generación de metadata para localización no funciona en la base de datos actual, agregar comaptibilidad.");
        }
        Set<String> ddls = new LinkedHashSet<>(10);
        Set<String> sqls = new LinkedHashSet<>(10);
        I18N.generateMetadata(ddls, sqls);
        final String ddl = StringUtils.join(ddls, " ");
        I18N.verifyLiquidbase();
        if (ddl != null && !ddl.isEmpty()) {
            LOGGER.error("UPDATING DATABASE SCHEMA WITH i18n TABLES WITH FOLLOWING QUERY\r\n /{}", ddl);
            dao.SQL_execute(ddl);
            new ConectionUtil().createReplicaDb(dao, ddl);         
            LOGGER.error("FINISHED UPDATING DATABASE SCHEMA WITH i18n TABLES");
        }  
        final String sql = StringUtils.join(sqls, " ");
        if (sql != null && !sql.isEmpty()) {
            LOGGER.error("UPDATING i18n DEFAULT DATA WITH FOLLOWING QUERY\r\n /{}", sql);
            dao.SQL_execute(sql);
            LOGGER.error("FINISHED UPDATING i18n DEFAULT DATA");
        }
    }
    
    private static void loadSupportedLanguages(IUntypedDAO dao) {
        SUPPORTED_LANGUAGES = new LinkedHashSet<>(2);
        SUPPORTED_LANGUAGES_SET = new LinkedHashSet<>(2);
        List<Map<String, String>> results = dao.HQL_findByQuery(""
                + " SELECT new Map(c.id as id, c.description as description) "
                + " FROM " + SupportedLanguages.class.getCanonicalName() + " c"
                + " WHERE c.id IN (" + LicenseUtil.filterSupportedLanguages() + ")");
        results.forEach(language -> {
            final String loc = language.get("id").replaceAll("-", "_").replaceAll(" ", "");
            SUPPORTED_LANGUAGES.add(loc);
            SUPPORTED_LANGUAGES_SET.add(new TextHasValue(language.get("description") + " (" + loc + ")", loc));
        });
        }

    private static void loadLocalizedEntities(ServletContext servletContext) throws SecurityException, NoSuchMethodException {
        for (EntityType<?> entityType : EntityCommon.loadEntityTypes(servletContext)) {
            final Class<?> mappedClass = entityType.getJavaType();
            if (!mappedClass.isAnnotationPresent(LocalizedEntity.class)) {
                continue;
            }
            final String mappedClassName = mappedClass.getCanonicalName();
            final String tableName = EntityCommon.loadTableName(mappedClass);
            ENTITY_TABLE_NAMES.put(mappedClassName, tableName);
            LocalizedEntityDTO localizedEntity = LOCALIZED_TABLES.get(tableName);
            Boolean compositePk = CompositeStandardEntity.class.isAssignableFrom(mappedClass);
            Set<LocalizedFieldDTO> localizedFields = LOCALIZED_FIELDS.get(tableName);
            Set<LocalizedFieldDTO> otherFields = NOT_LOCALIZED_FIELDS.get(tableName);
            
            for (Attribute<?, ?> a : entityType.getDeclaredAttributes()) {
                final Method getter = mappedClass.getDeclaredMethod(EntityCommon.fieldToGetter(a));
                Column column = getter.getAnnotation(Column.class); 
                String fieldName = a.getName().toLowerCase();
                if (column != null) {
                    fieldName = column.name().toLowerCase();
                }
                JoinColumn joinColumn = getter.getAnnotation(JoinColumn.class); 
                if (joinColumn != null) {
                    fieldName = joinColumn.name().toLowerCase();
                }
                if (getter.isAnnotationPresent(OneToMany.class)
                        || getter.isAnnotationPresent(ManyToMany.class)) {
                    continue;
                }
                final boolean isEmbeddedId = compositePk && getter.isAnnotationPresent(EmbeddedId.class);
                if (getter.isAnnotationPresent(Id.class)) {
                    localizedEntity = newLocalizedInstance(localizedEntity, tableName);
                    localizedEntity.setIdName(fieldName);
                } else if (isEmbeddedId) {
                    localizedEntity = newLocalizedInstance(localizedEntity, tableName);
                    localizedEntity.setCompositeIdNames(new LinkedHashSet<>(5));
                    for(Method method : a.getJavaType().getDeclaredMethods()) {
                        if (method.isAnnotationPresent(Column.class)) {
                            final String sqlName = method.getAnnotation(Column.class).name().toLowerCase();
                            final String hqlName = EntityCommon.methodToAttributeName(method.getName());
                            localizedEntity.getCompositeIdNames().add(new LocalizedFieldDTO(sqlName, hqlName, mappedClassName));
                        }
                    }
                }
                final LocalizedFieldDTO localizedField = new LocalizedFieldDTO(fieldName, a.getName(), mappedClassName);
                if (getter.isAnnotationPresent(LocalizedField.class)) {
                    if (localizedFields == null) {
                        localizedFields = new LinkedHashSet<>(5);
                    }
                    localizedFields.add(localizedField);
                } else if (!isEmbeddedId){
                    if (otherFields == null) {
                        otherFields = new LinkedHashSet<>(5);
                    }
                    if (localizedFields != null && localizedFields.contains(localizedField)) {
                        LOGGER.warn("Localized field {} is duplicated without @LocalizedField annotation for entity {}", fieldName, mappedClassName);
                    } else {
                    otherFields.add(localizedField);
                }
            }
            }
            localizedEntity = newLocalizedInstance(localizedEntity, tableName);
            if (compositePk) {   
                localizedEntity.setCompositeId(true);
            }
            if (localizedFields != null && !localizedFields.isEmpty()) {
                if (!LOCALIZED_TABLES.containsKey(tableName)) {
                    LOCALIZED_TABLES.put(tableName, localizedEntity);
                }
                LOCALIZED_FIELDS.put(tableName, localizedFields);
            }
            if (otherFields != null && !otherFields.isEmpty()) {
                if (!LOCALIZED_TABLES.containsKey(tableName)) {
                    LOCALIZED_TABLES.put(tableName, localizedEntity);
                }
                NOT_LOCALIZED_FIELDS.put(tableName, otherFields);
            }
        }
    }

    private static LocalizedEntityDTO newLocalizedInstance(LocalizedEntityDTO localizedEntity, String tableName) {
        if (localizedEntity == null) {
            return new LocalizedEntityDTO(tableName);
        }
        return localizedEntity;
    }
    
    private static String loadLocale() {
        final SessionViewer sessionViewer = new SessionViewer(false);
        if (sessionViewer.getRequest() == null) {
            return null;
        }
        String rquestLocale = sessionViewer.getRequest().getParameter("i18n-locale");
        if (rquestLocale != null) {
            return rquestLocale;
        }
        final Locale userLocale = sessionViewer.getLocale();
        return userLocale.toString();
    }

    private LocalizedUtil() {
    }

}
