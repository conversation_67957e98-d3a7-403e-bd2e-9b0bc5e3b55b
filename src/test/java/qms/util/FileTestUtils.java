
package qms.util;

import java.io.File;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Path;

public class FileTestUtils {
    
    public static Path loadPathByPath(Class targetClass, String filename) throws URISyntaxException {
       final String canonicalPackageName = targetClass.getCanonicalName().substring(0, targetClass.getCanonicalName().indexOf(targetClass.getSimpleName()));
       final String folderPath = "/" + canonicalPackageName.replaceAll("\\.", "/");
        final URL url = targetClass.getResource(folderPath + filename);
        if (url == null) {
            return null;
        }
        return new File(url.toURI()).toPath();
    }
    
}
