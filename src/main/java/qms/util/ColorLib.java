package qms.util;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.awt.Color;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import qms.framework.util.StringColorConfig;

/**
 *
 * <AUTHOR>
 */
public class ColorLib {

    public static final StringColorConfig DEFAULT_COLOR = new StringColorConfig("#000000", "#cccccc");

    private static final Pattern TWO_WORD_PATTERN = Pattern.compile("\\s(.){2}\\s", Pattern.MULTILINE);

    private static final Pattern TEXT_COLOR_PATTERN = Pattern.compile("#|\\s*", Pattern.MULTILINE);

    // RegExp: https://stackoverflow.com/a/62476772/1657465
    private static final Pattern INITIALS_VALUE_PATTERN = Pattern.compile("^\\s*(\\S)(?:.+?\\s(\\S)|(\\S)).*$", Pattern.MULTILINE);

    private static final LoadingCache<String, StringColorConfig> CACHE_STRING_COLORS = CacheBuilder.newBuilder()
            .expireAfterAccess(60, java.util.concurrent.TimeUnit.MINUTES)
            .maximumSize(100)
            .build(new CacheLoader<String, StringColorConfig>() {
                @Override
                public StringColorConfig load(final String name) {
                    return buildStringColors(name);
                }
            });
    
    private static final LoadingCache<String, String> CACHE_NAME_INITIALS = CacheBuilder.newBuilder()
            .expireAfterAccess(60, java.util.concurrent.TimeUnit.MINUTES)
            .maximumSize(100)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(final String name) {
                    return buildNameInitials(name);
                }
            });

    private static String textColor(String hexColor) {
        hexColor = TEXT_COLOR_PATTERN.matcher(hexColor).replaceAll("");
        return getContrastColor(hexColor);
    }

    public static String nameInitials(String name) throws ExecutionException {
        return nameInitials(name, null);
    }

    private static StringColorConfig buildStringColors(String str) {
        if (str == null || StringUtils.trimToEmpty(str).isEmpty()) {
            return DEFAULT_COLOR;
        }
        final StringColorConfig cache = new StringColorConfig();
        int hash = 0;
        for (int i = 0; i < str.length(); i++) {
            hash = str.charAt(i) + ((hash << 5) - hash);
        }
        hash = hash * 100;
        final StringBuilder colour = new StringBuilder("#");
        for (int i = 0; i < 3; i++) {
            int value = (hash >> (i * 8)) & 0xff;
            final String raddix = "00" + Integer.toString(value, 16);
            colour.append(raddix.substring(raddix.length() - 2));
        }
        cache.setBgColor(colour.toString());
        cache.setColor(textColor(colour.toString()));
        return cache;
    }
    
    public static String nameInitials(String name, String defaultValue) throws ExecutionException {
        if (name == null || StringUtils.trimToEmpty(name).isEmpty()) {
            return defaultValue;
        }
        final String result = CACHE_NAME_INITIALS.get(name);    
        if (StringUtils.trimToEmpty(result).isEmpty()) {
            return defaultValue;
        } else {
            return result;
        }
    }

    public static String buildNameInitials(String name) {
        if (name == null || StringUtils.trimToEmpty(name).isEmpty()) {
            return null;
        }
        String trimmedName = StringUtils.trimToEmpty(name);
        final Matcher regExp = TWO_WORD_PATTERN.matcher(trimmedName);
        if (regExp.matches()) {
            trimmedName = regExp.replaceAll(" ");// <-- Se eliminan palabras de dos letras
        }
        final Matcher initialsMatcher = INITIALS_VALUE_PATTERN.matcher(trimmedName);
        if (initialsMatcher.matches()) {
            final int groupCount = initialsMatcher.groupCount();
            if (groupCount > 0) {
                final String result;
                final String group1 = StringUtils.defaultIfBlank(initialsMatcher.group(1), "");
                final String group2 = StringUtils.defaultIfBlank(initialsMatcher.group(2), "");
                if (groupCount > 2) {
                    final String group3 = StringUtils.defaultIfBlank(initialsMatcher.group(3), "");
                    result = group1 + group2 + group3;
                } else if (groupCount > 1) {
                    result = group1 + group2;
                } else {
                    result = group1;
                }
                return result.toUpperCase();
            } else {
                return trimmedName.toUpperCase();
            }
        } else {
            return trimmedName.toUpperCase();
        }
    }

    public static String stringToColour(String str) throws ExecutionException {
        return stringColors(str).getBgColor();
    }

    public static StringColorConfig stringColors(String str) throws ExecutionException {
        return CACHE_STRING_COLORS.get(str);
    }

    /**
     * Get contrasting color based on input
     *
     * @param color color to contrast
     * @return #FFFFFF or #000000 depending on input
     */
    public static String getContrastColor(String color) {
        Color baseColor;
        try {
            baseColor = Color.decode(color.replace("#", "0X"));
        } catch (final NumberFormatException ex) {
            baseColor = new Color(Integer.parseInt(color.substring(1), 16));
        }
        double red = baseColor.getRed() * 0.299;
        double green = baseColor.getGreen() * 0.587;
        double blue = baseColor.getBlue() * 0.114;
        double index = red + green + blue;
        return index > 186 ? "#000000" : "#FFFFFF";
    }

}
