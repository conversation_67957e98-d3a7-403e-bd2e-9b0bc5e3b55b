/*Lista de solicitudes*/
.css_view, #eye_dataGrid_view, #dataGrid_view_header,
#eye_dataGrid_survey\.isFillRequestAvailable, .css_survey_isFillRequestAvailable,
#dataGrid_survey\.isFillRequestAvailable_header,#widget_dataGrid_survey\.isFillRequestAvailable,
#dataGrid_download_header,.css_download, #eye_dataGrid_download, label[for=survey\.isFillRequestAvailable],
#dataGrid_aprove_header, .css_aprove,  #eye_dataGrid_aprove,
#dataGrid_modify_header, .css_modify,  #eye_dataGrid_modify,
#dataGrid_cancel_header, .css_cancel,  #eye_dataGrid_cancel {
    display:none;
}
.scroll .dojoxCheckedMultiSelectWrapper {
    overflow-x: scroll;
}
#dataGrid td.css_dataGrid_edit img { 
    background: url("../../images/preview.png") no-repeat;
    display: inline-block;
    width: 0;
    height: 0;
    padding: 0.5rem;    
}