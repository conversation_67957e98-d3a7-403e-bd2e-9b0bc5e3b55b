package ape.pending.core;

import DPMS.DAOInterface.ISettingsDAO;
import DPMS.Mapping.Dual;
import DPMS.Mapping.IUser;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.dto.BaseJoinDTO;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.util.RecordRowsHQLBuilder;
import ape.pending.util.RecordRowsType;
import ape.pending.util.SqlQueryParser;
import bnext.exception.ExplicitRollback;
import bnext.reference.UserRef;
import jakarta.persistence.EntityManager;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.framework.core.EntityModelCache;
import qms.framework.dao.bean.OwnerHelper;
import qms.framework.util.CacheRegion;
import qms.util.Helper;
import qms.util.Translate;

/**
 *
 * <AUTHOR> Germán Lares Lares
 */
public class PendingHelper extends Helper {
       
    private static final Logger LOGGER = getLogger(Loggable.LOGGER.APE, PendingHelper.class);
   
    private static final Map<Module, List<APE>> TYPE_BY_MODULE = new HashMap<>();
    private static final LoadingCache<Long, Object> REFRESH_PENDINGS_LOCKS = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.DAYS)
            .maximumSize(10)
            .build(CacheLoader.from(Object::new));

    private static final Long REFRESH_PENDINGS_ID = 1L;
    
    private static final String 
            SELECT_BY_TYPE = ""
                + " SELECT type.id FROM "
                + PendingType.class.getCanonicalName() + " type "
                + " WHERE type.code = :type "
                + " AND type.status = :status";
    private static final String 
            RECORD_ID_FILTER = ""
                + " SELECT pnd.id "
                + " FROM  " + PendingRecord.class.getCanonicalName() + " pnd "
                + " WHERE "
                    + " pnd.status IN (:status) "
                    + " AND ("
                        + " owner IN (:owner) "
                        + " OR superOwnerId IN (:owner)"
                    + " ) ";
    private static final String 
            RECORD_ACTIVE_FILTER = " "
                + " AND ("
                    + " (record.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND record.owner IN (:owner)) "
                    + " OR (record.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND record.superOwnerId IN (:owner)) "
                    + " OR ("
                        + " record.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND record.superOwnerId IN (:owner)"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " record.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND record.owner IN (:owner)"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " )";
    private static final String
            RECORD_RESPONSIBLE_FILTER = " "
            + " AND ("
                + " (record.status = " + PendingRecord.STATUS.ATTENDED.getValue() + " AND record.owner IN (:owner)) "
                + " OR (record.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND record.owner IN (:owner)) "
                + " OR (record.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND record.superOwnerId IN (:owner)) "
                + " OR ("
                + " record.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                + " AND record.superOwnerId IN (:owner)"
                + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                + " ) "
                + " OR ("
                + " record.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                + " AND record.owner IN (:owner)"
                + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                + " ) "
            + " )";
    public static final String 
            PENDING_RECORD_SELECT_MAP = " "
                + " record.id AS id,"
                + " record.status AS status,"
                + " record.unattendedDays AS unattendedDays,"
                + " record.escalationLevel AS escalationLevel,"
                + " record.scalable AS scalable,"
                + " record.escalationFromCount AS escalationFromCount,"
                + " record.escalationMode AS escalationMode,"
                + " record.attendedOn AS attendedBy,"
                + " record.escalatedTo AS escalatedTo,"
                + " record.reassignedFrom AS reassignedFrom,"
                + " record.superOwnerId AS superOwnerId,"
                + " record.creationDate AS creationDate,"
                + " record.commitmentDate AS commitmentDate,"
                + " record.deadline AS deadline,"
                + " record.noticeDate AS noticeDate,"
                + " record.reminder AS reminder,"
                + " record.expired AS expired,"
                + " record.sendingExpiredMail AS sendingExpiredMail,"
                + " record.recordId AS recordId,"
                + " record.base AS base,"
                + " record.owner AS pendingRecordUserId,"
                + " ptype.id AS pendingTypeId,"
                + " ptype.code AS pendingTypeCode,"
                + " ptype.escalationDays AS escalationDays,"
                + " ptype.escalationLevels AS maxEscalationLevel,"
                + " ptype.flexibleAttendant AS flexibleAttendant,"
                + " ptype.escalationEnabled AS escalationEnabled,"
                + " ptype.escalationMode AS escalationModeType,"
                + " ptype.module AS module,"
                + " user.correo AS ownerMail,"
                + " user.code AS ownerCode,"
                + " user.description AS ownerName,"
                + " escalatedToHistory.code AS historyOwnerCode,"
                + " escalatedToHistory.description AS historyOwnerName,"
                + " escalatedToBoss.correo AS superOwnerMail,"
                + " escalatedToBoss.code AS superOwnerCode,"
                + " escalatedToBoss.description AS superOwnerName,"
                + " reassignedFrom.code AS reassignedFromCode,"
                + " reassignedFrom.description AS reassignedFromName,"
                + " reassignedFrom.correo AS reassignedFromMail,"
                + " escalatedFrom.code AS preOwnerCode,"
                + " escalatedFrom.description AS preOwnerName";
    public static final String
            PENDING_ENTITY_SELECT_MAP = ""
            + " entity.id AS recordId";
    public static final String
            PENDING_RECORD_FROM = ""
                + PendingRecord.class.getCanonicalName() + " record "
                + " CROSS JOIN " + Dual.class.getCanonicalName() + " dual "
                + " JOIN record.pendingType ptype "
                + " LEFT JOIN record.ownerUser user "
                + " LEFT JOIN record.superOwnerUser escalatedToBoss "
                + " LEFT JOIN record.preOwnerUser escalatedFrom "
                + " LEFT JOIN record.reassignedFromUser reassignedFrom "
                + " LEFT JOIN record.escalatedToUser escalatedToHistory ";
    public static final String
            ONLY_APE_FROM = ""
                + PendingRecord.class.getCanonicalName() + " record "     
            + " CROSS JOIN " + Dual.class.getCanonicalName() + " dual "
                + " JOIN record.pendingType ptype ";
    public static final String 
            SELECT_BY_TYPE_LIST = ""
                + " SELECT type.id FROM "
                + PendingType.class.getCanonicalName() + " type "
                + " WHERE type.code IN :type "
                + " AND type.status = :status"
            ;
    ;
    
    private static final String[] UNION_VALID_COLUMNS = {
        "id",
        "status",
        "unattendedDays",
        "escalationLevel",
        "scalable",
        "escalationFromCount",
        "escalationMode",
        "attendedBy",
        "escalatedTo",
        "superOwnerId",
        "ownerId",
        "creationDate",
        "commitmentDate",
        "deadline",
        "noticeDate",
        "reminder",
        "pendingTypeCode",
        "pendingTypeId",
        "escalationDays",
        "maxEscalationLevel",
        "flexibleAttendant",
        "escalationEnabled",
        "escalationModeType",
        "module",
        "ownerCode",
        "ownerName",
        "ownerMail",
        "historyOwnerCode",
        "historyOwnerName",
        "superOwnerCode",
        "superOwnerName",
        "superOwnerMail",
        "preOwnerCode",
        "preOwnerName",
        "reassignedFromName",
        "reassignedFromMail",
        "entity_code",
        "entity_description",
        "entity_id",
        "subEntity_code",
        "subEntity_description",
        "subEntity_rescheduled",
        "subEntityDepartmentName",
        "subEntityBusinessUnitName",
        "entityTypeCode",
        "entityTypeName",
        "entitySourceCode",
        "entitySourceName",
        "entityDepartmentName",
        "entityBusinessUnitName"
    };
    
    
    public static String getFilterRecordsByUser(ILoggedUser user, IPendingOperation ... pendings) {
        return getFilterRecordsByUser(new Long[] {user.getId()}, null, pendings);
    }
    
    public static String prepareFilterRecordsByUser(
            Long[] userIds,
            boolean includeCompleted,
            Boolean defineParameters,
            Map<String, Object> params, 
            IPendingOperation ... pendings
    ) {
        //TODO: Incluir atendidos
        // Usages in Project Files
        StringBuilder filter;
        if (includeCompleted) {
            filter = new StringBuilder(RECORD_RESPONSIBLE_FILTER);
        } else {
            filter = new StringBuilder(RECORD_ACTIVE_FILTER);
        }
        if (pendings.length > 0) {
            if (defineParameters) {
                params.put("owner", userIds);
            } else {
                final Matcher ownerMatcher = IPendingOperation.PARAM_PATTERN_OWNER.matcher(filter.toString());
                if (ownerMatcher.find()) {
                    filter.replace(0, filter.length(), ownerMatcher.replaceAll(StringUtils.join(userIds, ", ")));
                }
            }
        } else {      
            if (defineParameters) {
                params.put("owner", userIds);        
            } else {
                final Matcher ownerMatcher = IPendingOperation.PARAM_PATTERN_OWNER.matcher(filter.toString());
                if (ownerMatcher.find()) {
                    filter.replace(0, filter.length(), ownerMatcher.replaceAll(StringUtils.join(userIds, ", ")));
                }
            }
        }
        return replaceSingleUser(filter.toString(), userIds, defineParameters, params);
    }
    
    
    public static String getFilterRecordsByUser(Long userId, PendingRecord.STATUS[] statuses, IPendingOperation ... pendings) {
        return getFilterRecordsByUser(new Long[] { userId }, statuses, pendings);
    }
    
    public static String setFilterRecordsByUser(
            Long[] userIds, 
            PendingRecord.STATUS[] statuses, 
            Map<String, Object> params,
            IPendingOperation ... pendings
    ) {
        return prepareFilterRecordsByUser(userIds, statuses, true, null, pendings);
    }
    
    public static String getFilterRecordsByUser(
            Long[] userIds,
            PendingRecord.STATUS[] statuses,
            IPendingOperation ... pendings
    ) {
        return prepareFilterRecordsByUser(userIds, statuses, false, null, pendings);
    }
    
    public static String prepareFilterRecordsByUser(
            Long[] userIds,
            PendingRecord.STATUS[] statuses,
            Boolean defineParameters,
            Map<String, Object> params,
            IPendingOperation ... pendings
    ) {
        StringBuilder filter = new StringBuilder(RECORD_ID_FILTER);
        StringBuilder statusBuilder = new StringBuilder(5);
        if (statuses == null || statuses.length == 0) {
            statuses = new PendingRecord.STATUS[]{
                PendingRecord.STATUS.ACTIVE, PendingRecord.STATUS.ESCALATED, PendingRecord.STATUS.REASSIGNED
            };
        }
        if (statuses.length > 0) {
            if (defineParameters) {
                final List<Integer> statusValues = new ArrayList<>();
                for (PendingRecord.STATUS statuse : statuses) {
                    statusValues.add(statuse.getValue());
                }
                params.put("status", statusValues);
            } else {
                statusBuilder.append(statuses[0].getValue());
                for (int i = 1; i < statuses.length; i++) {
                    statusBuilder.append(',').append(statuses[i].getValue());
                }
            }
        }
        if (pendings.length > 0) {
            if (defineParameters) {
                final List<Long> typeValues = new ArrayList<>();
                for (IPendingOperation pending : pendings) {
                    typeValues.add(pending.getTypeId());
                }
                params.put("type", typeValues);
            } else {
                StringBuilder typeBuilder = new StringBuilder(pendings.length * 8);
                typeBuilder.append(pendings[0].getTypeId());
                for (int i = 1; i < pendings.length; i++) {
                    typeBuilder.append(',').append(pendings[i].getTypeId());
                }
                filter.append(
                    IPendingOperation.PARAM_PATTERN_TYPE.matcher(" AND pnd.type IN (:type) ").replaceAll(typeBuilder.toString())
                );   
            }
        }
        if (!defineParameters) {
            filter.replace(0, filter.length(), IPendingOperation.PARAM_PATTERN_STATUS.matcher(filter.toString()).replaceAll(statusBuilder.toString()));
            filter.replace(0, filter.length(), IPendingOperation.PARAM_PATTERN_OWNER.matcher(filter.toString()).replaceAll(StringUtils.join(userIds, ", ")));
        }        
        return replaceSingleUser(filter.toString(), userIds, defineParameters, params);
    }
    
    private static String replaceSingleUser(
            String filter,
            Long[] userIds, 
            Boolean defineParameters,
            Map<String, Object> params
    ) {
        // Parche para no afectar consultas de 1 solo usuario
        if (userIds.length == 1) {
            if (defineParameters) {
               filter = filter.replaceAll(
                   "\\.owner IN \\(:ownerId\\)",
                   ".owner = :owner"
               ).replaceAll(
                   "\\.superOwnerId IN \\(:ownerId\\)",
                   ".superOwnerId = :owner"
               ).replaceAll(
                   "\\.owner IN \\(:owner\\)",
                   ".owner = :owner"
               );  
               params.put("owner", userIds[0]);
            } else {
                filter = filter.replaceAll(
                    "\\.owner IN \\(" + userIds[0] + "\\)",
                    ".owner = " + userIds[0]
                ).replaceAll(
                    "\\.superOwnerId IN \\(" + userIds[0] + "\\)",
                    ".superOwnerId = " + userIds[0]
                );
            }
        }
        return filter;
    }
    
    
    public PendingHelper(IUntypedDAO dao) {
        super(dao);
    } 
    
    protected Long getTypeId(APE type) { 
        String query = SELECT_BY_TYPE;
        Map params = new HashMap();
        params.put("type", type.getCode());
        params.put("status", PendingType.STATUS.ACTIVE.getValue());
        return dao.HQL_findSimpleLong(query, params, true, CacheRegion.CATALOGS_SYSTEM, 0);
    }
    
    public List<Long> getTypeIdsByCodes(final Set<String> codes) { 
        final List<String> types = codes.stream()
                .collect(Collectors.toList());
        return getTypeIds(types);        
    }
    
    public List<Long> getTypeIdsByApe(final List<APE> apes) { 
        final List<String> types = apes.stream()
                .map(ape -> ape.getCode())
                .collect(Collectors.toList());
        return getTypeIds(types);
    }
    
    private List<Long> getTypeIds(final List<String> types) { 
        final Map<String, Object> params = new HashMap<>(2);
        params.put("type", types);
        params.put("status", PendingType.STATUS.ACTIVE.getValue());
        return dao.HQL_findByQuery(SELECT_BY_TYPE_LIST, params, true, CacheRegion.CATALOGS_SYSTEM, 0);
    }
    
    protected Set<UserRef> getOwnerUsers(Long ownerId) {
        return new OwnerHelper(dao).getOwnerUsers(ownerId);
    }
    
    public static List<APE> getTypesByModule(Module module) {
        List<APE> p;
        if (TYPE_BY_MODULE.containsKey(module)) {
            p = TYPE_BY_MODULE.get(module);
        } else {
            LOGGER.error("No se encontró el modulo de '{}' para pendientes, buscando de nuevo.", module.getKey());
            initialize();
            p = TYPE_BY_MODULE.get(module);
        }
        if (p == null) {
            LOGGER.error("No se encontaron pendientes para el modulo de '{}', buscando de nuevo.", module.getKey());
            initialize();
        }
        p = TYPE_BY_MODULE.get(module);
        if (p == null) {
            return Utilities.EMPTY_LIST;
        } else {
            return p;
        }
    }
    
    public static void initialize() {
        final List<APE> APE_CONSTANTS = Arrays.asList(APE.class.getEnumConstants());
        if (APE_CONSTANTS == null) {
            getLogger(Loggable.LOGGER.APE, PendingHelper.class).error("No se cargaron pendientes! (Se volverá a intentar en tiempo de ejecución en su momento)");
            return;
        }
        APE_CONSTANTS.stream().forEach(ar -> {
            for (Module module : Module.class.getEnumConstants()) {
                if(!TYPE_BY_MODULE.containsKey(module) || TYPE_BY_MODULE.get(module) == null) {
                    TYPE_BY_MODULE.put(module, new ArrayList<>(10));
                }
                if (ar.module().equals(module)) {
                    if(!TYPE_BY_MODULE.get(module).contains(ar)) {
                        TYPE_BY_MODULE.get(module).add(ar);
                    }
                }
            }
        });
        initializeApeRecords();
    }
    
    private static void initializeApeRecords() {
        final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class);
        try {
            if (Utilities.getSettings().getInitializedApe() != null 
                    && Utilities.getSettings().getInitializedApe().equals(1)) {
                return;
            }
            if (isRefreshPendingsBusy()) {
                LOGGER.error("Already refreshing pendings");
            } else {
                synchronized (getRefreshPendingsLock()) {
                    settingsDao.refreshAllPendings(null);
                    releaseRefreshpendingsLock();
                }
            }
            Integer result = updateInitializedApe(settingsDao, 1);
            if (result != null && result.equals(1)) {
                Utilities.resetSettings(null);
            }
        } catch (final Exception ex) {
            updateInitializedApe(settingsDao, 0);
            LOGGER.error("Ocurrió un error al inicializar, error: {}",ex);
        }
    }
    
    private static Integer updateInitializedApe(final ISettingsDAO settingsDao, final Integer initializedApe) {
        final Map<String, Object> currentValues = new HashMap<>(1);
        currentValues.put("initializedApe", initializedApe);
        final Integer result = settingsDao.setValueSetting(currentValues, null);
        return result;
    }
    
    public String userSetToNames(Set<UserRef> set) {
        StringBuilder names = new StringBuilder(50);
        Boolean first = true;
        for (UserRef user : set) {
            if (first) {
                names.append(", ");
                first = false;
            }
            names.append(user.getDescription());
        }
        return names.toString();
    }
    
    public static String getTypeCodeFilter(Class<? extends BaseAPE> entityClass, Boolean defineParameters, Map<String, Object> params) {
        Set<APE> apes = EntityModelCache.getAPE(entityClass);
        if (apes.isEmpty()) {
            LOGGER.error("APE is NOT fully implemented for entity '{}', theres no APE pendings related to it.", entityClass);
            return "''";
        }
        StringBuilder filter = new StringBuilder(100);
        if (defineParameters) {
            filter.append(":typeCodes");
            final List<String> apeCodes = apes.stream().map((ape -> ape.getCode())).collect(Collectors.toList());
            params.put("typeCodes", apeCodes);
        } else {
            for (APE ape : apes) {
                filter.append(",'").append(ape.getCode()).append("'");
            }
            filter.deleteCharAt(0);
        }
        return filter.toString();
    }
    
    public static String getTypeIdFilter(IPendingOperation[] pendingOperations, Boolean defineParameters, Map<String, Object> params) {
        if (pendingOperations == null || pendingOperations.length == 0) {
            return "0";
        }
        StringBuilder filter = new StringBuilder(10);
        if (defineParameters) {
            filter.append(":typeIds");
            final List<Long> typeIds = new ArrayList<>();
            for (IPendingOperation pendingOperation : pendingOperations) {
                final Long typeId = pendingOperation.getTypeId();
                if (typeId == null) {
                    throw new RuntimeException("Missing APE entity type ID for  '" + pendingOperations[0].getApe().getCode() + "'");                    
                }
                typeIds.add(typeId);
            }
            params.put("typeIds", typeIds);

        } else {
            filter.append(pendingOperations[0].getTypeId());
            for (int i = 1; i < pendingOperations.length; i++) {
                final Long typeId = pendingOperations[i].getTypeId();
                if (typeId == null) {
                    throw new RuntimeException("Missing APE entity type ID for  '" + pendingOperations[0].getApe().getCode() + "'");                    
                }
                filter.append(",'").append(typeId).append("'");
            }
        }
        return filter.toString();
    }
    
    public static boolean isBaseAPE(Class entityClass) {
        return EntityModelCache.isBase(entityClass);
    }
    
    public static String getRowsHQL(Class entityClass) {
        BaseJoinDTO b = EntityModelCache.getRecordBaseJoinDTO(entityClass.getCanonicalName());
        String HQL = b.getHQL()
            .replace("and  subEntity.id = record.recordId", "")
            .replace("and  entity.id = record.recordId", "")
            .replace("AND  subEntity.id = record.recordId", "")
            .replace("AND  entity.id = record.recordId", "")
            .replace("subEntity.id = record.recordId  and", "")
            .replace("entity.id = record.recordId  and", "")
            .replace("subEntity.id = record.recordId  AND", "")
            .replace("entity.id = record.recordId  AND", "")
        ;
        for(Module module : Module.allModules()) {
            HQL = HQL
                .replace("and  '" + module.getKey() + "' = record.module", "")
                .replace("AND  '" + module.getKey() + "' = record.module", "")
                .replace("'" + module.getKey() + "' = record.module  and", "")
                .replace("'" + module.getKey() + "' = record.module  AND", "")
            ;
        }
        return HQL;
    }

    public static String getRecordRowsHQL(
            IUser user,
            Class entityClass,
            RecordRowsType type,
            IPendingOperation ... pendings
    ) {
        BaseJoinDTO b = EntityModelCache.getRecordBaseJoinDTO(entityClass.getCanonicalName());
        final Long[] users = user != null ? new Long[]{user.getId()} : null;
        return getRecordRowsHQL(
                null,
                users,
                b,
                entityClass,
                null,
                type,
                null,
                null,
                false,
                false,
                null,
                pendings
        );
    }

    /**
     *
     */
    public static String getLightActiveRecordsHQL(
            Class<? extends BaseAPE> entityClass,
            String owner,
            Long[] userIds,
            RecordRowsType type,
            List<Long> recordIds,
            boolean includeCompleted,
            Boolean defineParameters,
            Map<String, Object> params,
            IPendingOperation ... pendings
    ) {
        Module module = null;
        for (IPendingOperation pending : pendings) {
            if (module == null) {
                module = pending.getApe().getModule();
            } else if (pending.getApe().getModule() == null || !pending.getApe().getModule().equals(module)) {
                throw new ExplicitRollback(" "
                    + " Incompatible APE pendings, they all must have the"
                    + " '" + module + "' module as a 'Base',"
                    + " pending '" + pending.getClass().getCanonicalName() + " has "
                    + " '" + pending.getImplementedModule() + "'."
                );
                
            }
            if (!pending.getEntityBase().equals(entityClass)) {
                throw new ExplicitRollback(" "
                    + " Incompatible APE pendings, they all must have the"
                    + " '" + entityClass.getCanonicalName() + "' as a 'Base',"
                    + " pending '" + pending.getClass().getCanonicalName() + " has "
                    + " '" + pending.getEntityBase().getCanonicalName() + "'."
                );
            }
        }
        if (module == null) {
            throw new ExplicitRollback(" "
                + " Not defined APE pendings, the method must be called for"
                + " '" + entityClass.getCanonicalName() + "' with at least one APE pending."
            );
        }
        BaseJoinDTO b = EntityModelCache.getLightRecordBaseJoinDTO(module, entityClass.getCanonicalName());
        return getRecordRowsHQL(
                module,
                userIds,
                b,
                entityClass,
                owner,
                type,
                null,
                recordIds,
                includeCompleted,
                defineParameters,
                params,
                pendings
        );
    }
    private static String getRecordRowsHQL(
            Module module,
            Long[] userIds,
            BaseJoinDTO b, 
            Class entityClass,
            String owner,
            RecordRowsType type,
            APE ape, 
            List<Long> recordIds,
            boolean includeCompleted,
            Boolean defineParameters,
            Map<String, Object> params,
            IPendingOperation ... pendings
    ) {
        if (!PendingHelper.isBaseAPE(entityClass)) {
            LOGGER.error("The parameterized class '{}' is not related to APE pendings, please ensure that the entity is at 'APE.java' and implements 'BaseAPE.java' inteface.", entityClass);
            return Utilities.EMPTY_STRING;
        }
        if (module == null && pendings != null && pendings.length > 0) {
            module = pendings[0].getApe().getModule();
        }
        RecordRowsHQLBuilder builder = new RecordRowsHQLBuilder(
                module,
                pendings,
                userIds,
                b, 
                entityClass, 
                owner,
                type,
                ape,
                recordIds,
                includeCompleted,
                defineParameters, 
                params
        );
        return builder.getHql();
    }
    
    public static String getRecordRowsSQL(EntityManager entityManager, List<String> columns,  Module module) {
        List<BaseJoinDTO> HQLS = EntityModelCache.getRecordRowsHQL(module);
        return getRecordRowsSQL(entityManager, HQLS, columns);
    }
    
    public static String getRowsSQL(EntityManager entityManager, Class entityClass) {
        return getRowsSQL(entityManager, entityClass, "");
    }
    
    public static String getRowsSQL(EntityManager entityManager, Class entityClass, String appendEndHql) {
        BaseJoinDTO b = EntityModelCache.getRecordBaseJoinDTO(entityClass.getCanonicalName());
        if(b == null || b.getColumns()== null) {
            EntityModelCache.reset(null);
            b = EntityModelCache.getRecordBaseJoinDTO(entityClass.getCanonicalName());
            if(b == null || b.getColumns()== null) {
                throw new RuntimeException("Missing APE model for entity class '" + entityClass.getCanonicalName() + "'");
            }
        }
        String sql = Translate.hqlToSqlString(entityManager, b.getInjectedHQL() + appendEndHql);
        sql = Translate.normalizeColumns(sql, UNION_VALID_COLUMNS);
        return sql;
    }

    private static String getRecordRowsSQL(EntityManager entityManager, List<BaseJoinDTO> HQLS, List<String> columns) {
        StringBuilder sqlBuilder = null;
        String tempSql = null;
        int L = HQLS.size();
        if(L > 0) {
            tempSql = Translate.hqlToSqlString(entityManager, HQLS.get(0).getInjectedHQL());
            for (int i = 1; i < HQLS.size(); i++) {
                String HQL = HQLS.get(i).getInjectedHQL();
                if (i%2 != 0) {
                    if(sqlBuilder == null) {
                        sqlBuilder = new StringBuilder(
                            Translate.getSQLUnion(Translate.hqlToSqlString(entityManager, HQL), tempSql)
                        );
                    } else {
                        sqlBuilder.append(" UNION ALL ").append(
                            Translate.getSQLUnion(Translate.hqlToSqlString(entityManager, HQL), tempSql)
                        );
                    }
                } else {
                    tempSql = Translate.hqlToSqlString(entityManager, HQL);
                }
            }
        }
        if (sqlBuilder == null && tempSql == null) {
            return Utilities.EMPTY_STRING;
        } else if (sqlBuilder == null) {
            StringBuilder cols = new StringBuilder(UNION_VALID_COLUMNS.length * 10);
            cols.append("NULL").append(SqlQueryParser.AS).append(UNION_VALID_COLUMNS[0]);
            for (int i = 1; i < UNION_VALID_COLUMNS.length; i++) {
                cols.append(", NULL").append(SqlQueryParser.AS).append(UNION_VALID_COLUMNS[i]);
            }
            String SQL = Translate.getSQLUnion(cols.insert(0, SqlQueryParser.SELECT).append(SqlQueryParser.FROM).append("dual").toString(), tempSql);
            return SQL.substring(0, SQL.lastIndexOf("UNION ALL"));
        }
        return sqlBuilder.toString();
    }
    
    public boolean isPendingValidById(final Long pendingRecordId, final APE ape, final Long userId) {
        if (ape.getOperationClass() == null) {
            return true;
        }
        final Map<String, Object> params = new HashMap<>(2);
        params.put("id", pendingRecordId);
        params.put("userId", userId);
        String valid = dao.HQL_findSimpleString(""
            + " SELECT 1"
            + " FROM " 
                + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " WHERE pnd.id = :id"
            + " AND ("
                + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = :userId) "
                + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = :userId) "
                + " OR ("
                    + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                    + " AND pnd.superOwnerId = :userId"
                    + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                + " ) "
                + " OR ("
                    + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                    + " AND pnd.owner = :userId"
                    + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                + " ) "
            + " ) ",
            params
        );
        return !valid.isEmpty();
    }
    
    public boolean isPendingValidByRecordId(final Long recordId, final APE ape, final Long userId) {
        if (ape == null || ape.getOperationClass() == null) {
            return true;
        }
        final Map<String, Object> params = new HashMap<>(3);
        params.put("recordId", recordId);
        params.put("ape", ape.getCode());
        params.put("userId", userId);
        String valid = dao.HQL_findSimpleString(""
            + " SELECT 1"
            + " FROM " 
                + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " WHERE pnd.recordId = :recordId"
            + " AND ptype.code = :ape"
            + " AND ("
                + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = :userId) "
                + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = :userId) "
                + " OR ("
                    + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                    + " AND pnd.superOwnerId = :userId"
                    + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                + " ) "
                + " OR ("
                    + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                    + " AND pnd.owner = :userId"
                    + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                + " ) "
            + " ) ",
            params
        );
        return !valid.isEmpty();
    }
    
    public APE getPendingRecordAPE(Long pendingRecordId) {
        if (pendingRecordId == null || pendingRecordId <= 0) {
            return null;
        }
        String typeCode = dao.HQL_findSimpleString(""
            + " SELECT t.code"
            + " FROM " + PendingRecord.class.getCanonicalName() + " c"
            + ',' + PendingType.class.getCanonicalName() + " t"
            + " WHERE c.id = " + pendingRecordId
            + " AND c.type = t.id"
        );
        APE ape;
        if (typeCode.startsWith("ACC-")) {
            ape = APE.valueOf(typeCode.substring(4).replace("-", "_"));
        } else {
            ape = APE.valueOf(typeCode.replace("-", "_"));
        }
        return ape;
    }

    public static Boolean isRefreshPendingsBusy() {
        return REFRESH_PENDINGS_LOCKS.getIfPresent(REFRESH_PENDINGS_ID) != null;
    }

    public static Object getRefreshPendingsLock() {
        return REFRESH_PENDINGS_LOCKS.getUnchecked(REFRESH_PENDINGS_ID);
    }

    public static void releaseRefreshpendingsLock() {
        try {
            if (REFRESH_PENDINGS_LOCKS.getIfPresent(REFRESH_PENDINGS_ID) == null) {
                return;
            }
            REFRESH_PENDINGS_LOCKS.invalidate(REFRESH_PENDINGS_ID);
        } catch (final Exception e) {
            LOGGER.error(
                    "Failed to release refresh pendings lock {}",
                    new Object[]{e}
            );
        }
    }
}
