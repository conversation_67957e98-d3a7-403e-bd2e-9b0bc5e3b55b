package qms.framework.bulk.imp;

import DPMS.DAOInterface.IFilesDAO;
import Framework.Config.Utilities;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Locale;
import java.util.Map;
import javax.annotation.Nonnull;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import qms.access.dto.ILoggedUser;
import qms.form.bulk.logic.FormReportBulkUploader;
import qms.framework.bulk.util.BulkError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.AboutApp;
import qms.framework.util.ExcelParser;
import qms.framework.util.MeasureTime;
import qms.framework.util.SettingsUtil;
import qms.util.QMSException;

public class FormBulkHandler extends ReportBulkBaseHandler {

    private static final Logger LOGGER = Loggable.getLogger(FormBulkHandler.class);

    private final String documentMasterId;

    public static final String SCHEMA = "form_report_bulk";
    private static String getNewBulkTableName(
            final String documentMasterId
    ) {
        return getNewBulkTableName(documentMasterId, null);
    }

    public static String getNewBulkTableName(
            final String documentMasterId, String slimReportCode
    ) {
        StringBuilder tableName = new StringBuilder("FORM_BULK_");
        if (slimReportCode == null) {
            tableName.append("{slimReportCode}");
        } else {
            tableName.append(slimReportCode);
        }
        tableName.append("_").append(documentMasterId);
        return tableName.toString()
                .replaceAll("-", "")
                .replaceAll(" ", "");
    }

    public FormBulkHandler(
            final Locale locale,
            final Long databaseQueryId,
            final Long reportId,
            @Nonnull final Long surveyId,
            final String documentMasterId,
            final String excelIdFields,
            final Long slimReportId,
            final String slimReportCode,
            final ILoggedUser loggedUser
    ) throws QMSException {
        super(
                new FormReportBulkUploader(
                        locale,
                        databaseQueryId,
                        reportId,
                        surveyId,
                        SettingsUtil.getAppUrl(),
                        getNewBulkTableName(documentMasterId),
                        SCHEMA,
                        AboutApp.getAppName(),
                        documentMasterId,
                        excelIdFields,
                        slimReportId,
                        slimReportCode,
                        loggedUser
                )
        );
        this.documentMasterId = documentMasterId;
    }

    public FormBulkHandler(
        final Locale locale,
        final Long reportId,
        @Nonnull final Long surveytId,
        final String documentMasterId,
        final String url,
        final String appName,
        final ILoggedUser loggedUser
    ) throws QMSException {
        super(
                new FormReportBulkUploader(
                        locale,
                        reportId,
                        surveytId,
                        url,
                        getNewBulkTableName(documentMasterId),
                        SCHEMA,
                        appName,
                        documentMasterId,
                        loggedUser
                )
        );
        this.documentMasterId = documentMasterId;
    }

    @Override
    public String getSchema() {
        return SCHEMA;
    }

    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public ResponseEntity<Map<String, Object>> uploadTemplate(
            @Nonnull HttpServletResponse response,
            @Nonnull MultipartFile multipart,
            @Nonnull ILoggedUser loggedUser
    ) throws QMSException, IOException, BulkError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
            try {
            final ResponseEntity<Map<String, Object>> result = super.uploadTemplate(multipart, loggedUser);
            if (result.getStatusCode() == HttpStatus.OK) {
                // En caso de éxito, solo devuelve respuesta
                return result;
            }
            // En caso de falla descarga automáticamente el archivo de error
            final Map<String, Object> body = result.getBody();
            final Long resultFileId = (Long) body.get("resultFileId");
            final String filename = (String) body.get("filename");
            this.getFileManager().writeHeaders(
                    response,
                    filename,
                    ExcelParser.EXCEL_2007_CONTENT_TYPE,
                    null,
                    false,
                    null,
                    null
            );
            IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            try (final ServletOutputStream output = response.getOutputStream()) {
                dao.writeContentToOutput(resultFileId, output);
            } catch (final IOException | SQLException e) {
                LOGGER.error("Failed to download template load result", e);
                return new ResponseEntity<>(HttpStatus.EXPECTATION_FAILED);
            }
            return result;
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in activity bulk write template.");
        }

    }
}
