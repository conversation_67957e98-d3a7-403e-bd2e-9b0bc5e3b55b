/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Framework.DAO;

import java.sql.Types;
import org.hibernate.dialect.OracleDialect;
import org.hibernate.type.descriptor.jdbc.JdbcType;
import org.hibernate.type.descriptor.jdbc.spi.JdbcTypeRegistry;

/**
 *
 * <AUTHOR> G<PERSON>za <PERSON>
 */
public class BnextOracleDialect extends OracleDialect {

    @Override
    public JdbcType resolveSqlTypeDescriptor(String columnTypeName, int jdbcTypeCode, int precision, int scale, JdbcTypeRegistry jdbcTypeRegistry) {
        switch (jdbcTypeCode) {
            case Types.NVARCHAR:
                jdbcTypeCode = Types.LONGVARCHAR;
                break;
            case -101:
                jdbcTypeCode = Types.TIMESTAMP;
                break;
        }
        return super.resolveSqlTypeDescriptor(columnTypeName, jdbcTypeCode, precision, scale, jdbcTypeRegistry);
    }

}
