@use 'src/styles/immutable-colors' as *;
.dropdown-button {
  display: flex;
}
.selected-item {
  padding-right: 0.3rem;
  padding-left: 0.3rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  min-width: auto;

  > .igx-icon {
    margin-left: 0.25rem;
    display: inline-flex;
    justify-content: center;
  }
  > label {
    margin-right: 0.25rem;
    margin-left: 0.5rem;

    &.no-available-items {
      margin-right: 0.75rem;
    }
  }
}
.toggle-menu {
  padding-right: 0.3rem;
  padding-left: 0.3rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  min-width: 0px;
  letter-spacing: normal;
  text-transform: none;
}
.dropdown-button-menu {
  min-width: 17rem;
  max-width: 25.313rem;
}
.menu-button {
  padding: 0 1.5rem;
  width: 100%;
  height: 2.5rem;
  line-height: 2.5rem;

  .igx-icon {
    margin-right: 0.75rem;
    vertical-align: middle;
  }
  a {
    color: $fg-color;
  }
}

::ng-deep app-dropdown-button {
  &.igx-icon-button--outlined,
  &.igx-icon-button--contained,
  &.igx-button--outlined,
  &.igx-button--contained {
    padding: 0px !important;
  }
}
::ng-deep .drop-down-menu-button.igx-drop-down__item {
  padding: 0;
}
:host ::ng-deep {
  .round-button {
    cursor: pointer;
    box-shadow:
      rgba($fg-color, 0.26) 0px 1px 0.313rem 0px,
      rgba($fg-color, 0.12) 0px 0.125rem 0.125rem 0px,
      rgba($fg-color, 0.08) 0px 0.188rem 1px -0.125rem;
    border-radius: 1.15rem;

    &.no-shadow {
      box-shadow: none;
    }

    .selected-item {
      border-top-right-radius: 0rem;
      border-bottom-right-radius: 0rem;
      box-shadow: none;
    }
    .toggle-menu {
      border-top-left-radius: 0rem;
      border-bottom-left-radius: 0rem;
      box-shadow: none;
      padding-right: 0.5rem;
      border-left-width: 0;
    }
  }

  .no-available-drowpdown-items.round-button .selected-item {
    border-top-right-radius: 1.75rem;
    border-bottom-right-radius: 1.75rem;
  }
}

.toggle-menu.igx-icon-button--cosy,
.toggle-menu.igx-button--cosy {
  margin: 0;
}
.toggle-menu.igx-icon-button,
.toggle-menu.igx-button {
  margin: 0;
}
