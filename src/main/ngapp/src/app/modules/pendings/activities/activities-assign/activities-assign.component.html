<igx-dialog #addDialog [title]="'dialog-assign-title' | translate: this" (opened)="onOpened()">
  <form [formGroup]="addForm" class="form grid-container">
    <!-- Implementador -->
    <app-dropdown-search
      name="addImplementer"
      formControlName="addImplementer"
      iconName="edit_calendar"
      [valueFormControl]="addForm.controls.addImplementer"
      [displayDensity]="displayDensity"
      [scrollIntoView]="false"
      [openDropdownOnIconClick]="false"
      [required]="true"
      [label]="'activity-implementer' | translate: this"
      [options]="localImplementers"
      [hint]="implementerHint"
      (changed)="onChangeImplementer($event)"
      (prefixClick)="onPrefixImplementerClick($event)"
      [title]="'impl-calendar' | translate: this"
    >
    </app-dropdown-search>
    <!-- Verificador -->
    @if (constraints()?.verificationReqOnPlanning && constraints()?.addVerificationAvailable && addForm.controls.addVerifier) {
      <app-dropdown-search
        name="addVerifier"
        formControlName="addVerifier"
        iconName="edit_calendar"
        [displayDensity]="displayDensity"
        [valueFormControl]="addForm.controls.addVerifier"
        [scrollIntoView]="false"
        [openDropdownOnIconClick]="false"
        [required]="true"
        [label]="'activity-verifier' | translate: this"
        [options]="localVerifiers"
        [hint]="verifierHint"
        (changed)="onChangeVerifier($event)"
        (prefixClick)="onPrefixVerifierClick($event)"
        [title]="'ver-calendar' | translate: this"
      >
      </app-dropdown-search>
    }
    <!-- Horas estimadas -->
    <igx-input-group [ngClass]="displayDensityClass" type="line" class="text-field" theme="material">
      <input igxInput name="addEstimatedHours" formControlName="addEstimatedHours" type="number" />
      <label igxLabel>{{ 'activity-plannedHours' | translate: this }}</label>
    </igx-input-group>
    <!-- Fecha de implementación -->
    <igx-date-picker
      #addStartDatePicker
      name="addStartDate"
      formControlName="addStartDate"
      [ngClass]="displayDensityClass"
      [disabledDates]="implementationDisabledDates()"
      (valueChange)="onSelectImplementation($event)"
      [overlaySettings]="overlaySettings"
      [resourceStrings]="datePickersTitles"
      mode="dialog"
    >
      <label igxLabel>{{ 'activity-implementation' | translate: this }}:</label>
    </igx-date-picker>
    <!-- Fecha de verificación -->
    @if (constraints()?.verificationReqOnPlanning && constraints()?.addVerificationAvailable) {
      <igx-date-picker
        #addEndDatePicker
        name="addEndDate"
        formControlName="addEndDate"
        [ngClass]="displayDensityClass"
        [disabledDates]="verificationDisabledDates()"
        (valueChange)="onSelectVerification($event)"
        [overlaySettings]="overlaySettings"
        [resourceStrings]="datePickersTitles"
        mode="dialog"
      >
        <label igxLabel>{{ 'activity-verification' | translate: this }}:</label>
      </igx-date-picker>
    }
    <!-- Orden -->
    <igx-input-group [ngClass]="displayDensityClass" type="line" class="text-field" theme="material">
      <input igxInput name="addOrder" formControlName="addOrder" type="text" [maxlength]="8" (keypress)="onKeyPress($event)" />
      <label igxLabel>{{ 'i18n.activity-addOrder' | translate: this }}</label>
    </igx-input-group>
    <!-- Es planeada -->
    <igx-switch #isPlanned name="isPlanned" formControlName="isPlanned"> {{ 'i18n.activity-isPlanned' | translate: this }} </igx-switch>
  </form>
  <div igxDialogActions>
    <button [ngClass]="displayDensityClass" [igxButton]="'flat'" (click)="onCloseAssign()" tabindex="-1">{{ 'root.common.button.cancel' | translate: this }}</button>
    @if (isFormValid) {
      <button [ngClass]="displayDensityClass" [igxButton]="'flat'" (click)="onAssign()" tabindex="-1">
        {{ 'root.common.button.save' | translate: this }}
      </button>
    }
  </div>
</igx-dialog>
