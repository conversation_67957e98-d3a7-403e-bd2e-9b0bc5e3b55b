define(['bnext/callMethod', 'dojo/_base/json', 'dojo/dom', 'bnext/survey/_util/jsFailureExposure!'],
        function(callMethod, JSON, dom) {

            var Agent = function() {
                this.debug = false;
                this.call = function() {
                    var METHOD = "";
                    var CALLBACK = "";
                    var SERVICE_STORE = "";
                    if (arguments.length < 3) {
                        alert("Incorrect number of parameters. Please check your function call");
                        return;
                    }
                    SERVICE_STORE = arguments[0];
                    METHOD = arguments[1];
                    CALLBACK = arguments[2];


                    if (!SERVICE_STORE) {
                        SERVICE_STORE = 'Survey.action';;
                    }
                    var INNER_PARAMS = [];
                    if (METHOD === "readObject") {
                        INNER_PARAMS.push(dom.byId('id').value * 1);
                    }
                    var argumentos = ({
                        url: SERVICE_STORE,
                        method: METHOD,
                        params: INNER_PARAMS,
                        errback: function(e) {
                            errorCallBack(e);
                        },
                        callback: function(r) {
                            if(typeof CALLBACK === 'function') {
                                if ((r) + '' === '[object Object]') {
                                    (CALLBACK)(r);
                                } else {
                                    (CALLBACK)(JSON.fromJson(r));
                                }
                            } else if (dom.byId(CALLBACK) && typeof CALLBACK === 'string') {
                                console.log('lang.byId(CALLBACK) : ' + dom.byId(CALLBACK));
                                try {
                                    dom.byId(CALLBACK).innerHTML = r;
                                } catch (e) {
                                    dom.byId(CALLBACK).value = r;
                                }
                            } else {
                                if ((r) + '' === '[object Object]') {
                                    eval(CALLBACK)(r);
                                } else {
                                    eval(CALLBACK)(JSON.fromJson(r));
                                }
                            }
                        }
                    });
                    return callMethod(argumentos);
                };
                this.listen = function(aa_event, CALLBACK) {
                    // listener function will come here
                };
            };
            
            return Agent;
        });