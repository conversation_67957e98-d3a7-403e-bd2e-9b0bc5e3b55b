package qms.framework.util;

import java.util.List;

public class PaginateUtil {
    
    public final static Double MAX_RECORDS_PER_PAGE = 1000.0;

    public static List<Long> definePagedRecordIds(final Integer currentPage, final List<Long> recordIds) {
        if (recordIds == null || recordIds.isEmpty() || currentPage == null) {
            return recordIds;
        }
        final Double currentStart = currentPage * MAX_RECORDS_PER_PAGE;
        final Integer maxPosition = recordIds.size();
        if (maxPosition > 0) {
            final Integer currentPageMaxPosition = currentStart.intValue() + MAX_RECORDS_PER_PAGE.intValue();
            if (currentPageMaxPosition > maxPosition) {
                final List<Long> subRecordIds = recordIds.subList(currentStart.intValue(), maxPosition);
                return subRecordIds;
            } else {
                final List<Long> subRecordIds = recordIds.subList(currentStart.intValue(), currentPageMaxPosition);
                return subRecordIds;
            }
        } else {
            return recordIds;
        }
    }

    public static Double numberPages(Integer recordsSize) {
        final Double size = Math.ceil(recordsSize > MAX_RECORDS_PER_PAGE ? recordsSize / MAX_RECORDS_PER_PAGE : 1.0);
        return size;
    }
    
}
